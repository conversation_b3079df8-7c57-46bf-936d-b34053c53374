*** Settings ***
Documentation    Common setup and teardown keywords using built-in libraries
Library          Browser
Library          RequestsLibrary
Library          Collections
Library          OperatingSystem
Library          String
Library          ../../libraries/DateUtils.py
Variables        ../../config/variables.py
Library          ../../libraries/TestDataLoader.py

*** Keywords ***
Setup Browser Session
    [Documentation]    Initialize browser with configuration from variables
    [Arguments]    ${headless}=${HEADLESS}

    # Create new browser instance
    New Browser    browser=${BROWSER}    headless=${headless}    slowMo=${SLOWMO}

    # Set viewport size
    ${viewport}=    Create Dictionary    width=${VIEWPORT_WIDTH}    height=${VIEWPORT_HEIGHT}
    New Context    viewport=${viewport}

    # Create new page and navigate to base URL
    New Page    ${BASEURL_HUURDER_TST}

    # Set default timeouts
    Set Browser Timeout    ${DEFAULT_TIMEOUT}

Authenticate User
    [Documentation]    Login user and save authentication state
    [Arguments]    ${user_role}=user

    # Get user data from YAML
    ${user_data}=    Get User Data    ${user_role}

    # Navigate to login if not already there
    ${current_url}=    Get Url

    # Fill login form using Browser library
    Fill Text    input[name="email"]    ${user_data}[email]
    Fill Text    input[name="password"]    ${user_data}[password]
    Click    button[type="submit"]

    # Wait for successful login
    Wait For Elements State    text="User Profile"    visible    timeout=${ELEMENT_TIMEOUT}


Take Screenshot With Timestamp
    [Documentation]    Take screenshot with timestamp in filename
    [Arguments]    ${name}=screenshot

    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${filename}=    Set Variable    ${name}_${timestamp}
    Take Screenshot    filename=${filename}
    RETURN    ${filename}

Verify Element Text Contains
    [Documentation]    Verify element contains expected text using Browser library
    [Arguments]    ${locator}    ${expected_text}    ${timeout}=${ELEMENT_TIMEOUT}

    Wait For Elements State    ${locator}    visible    timeout=${timeout}
    ${actual_text}=    Get Text    ${locator}
    Should Contain    ${actual_text}    ${expected_text}    ignore_case=True

Verify Element Is Visible
    [Documentation]    Verify element is visible with optional screenshot
    [Arguments]    ${locator}    ${take_screenshot}=${True}    ${timeout}=${ELEMENT_TIMEOUT}

    Wait For Elements State    ${locator}    visible    timeout=${timeout}

    IF    ${take_screenshot}
        Take Screenshot With Timestamp    element_visible
    END

Close Browser Session
    [Documentation]    Clean up browser resources
    Close Browser

Setup Test Environment
    [Documentation]    Complete test environment setup
    [Arguments]    ${user_role}=user    ${headless}=${HEADLESS}

    # Setup browser and API
    Setup Browser Session    headless=${headless}

    # Authenticate user
    Authenticate User    ${user_role}

Teardown Test Environment
    [Documentation]    Complete test environment cleanup

    Close Browser Session

Klik op knop met tekst
    [Arguments]    ${tekst}
    Click    //button[.='${tekst}']

Klantdialoog vraag en antwoord Generic
    [Documentation]    Klantdialoog vraag en antwoord
    ${vraag}=   Get Text    h2[class="font-display mb-4 text-3xl"]
    ${antwoord}=    Get Antwoord Voor Vraag    ${vraag}
    Klik op knop met tekst    ${antwoord}
    Wait For Response    matcher=https://dealliantie.zandbak.website.databalk.app/api/flow/continue    timeout=${DEFAULT_TIMEOUT}


Klantdialoog vraag en antwoord
    [Arguments]    ${vraag}    ${antwoord}
    Get Text    h2[class="font-display mb-4 text-3xl"]    equal    ${vraag}
    Klik op knop met tekst    ${antwoord}
    # Wait For Elements State    css=div[role="status"]    hidden    timeout=30s
    Wait For Response    matcher=https://dealliantie.zandbak.website.databalk.app/api/flow/continue    timeout=10s

