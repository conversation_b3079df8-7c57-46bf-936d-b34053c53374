"""
Locators specifiek voor de Huurcontract Beëindiging flow.
Georganiseerd per stap in de flow voor betere onderhoudbaarheid.
"""

from ..common.CommonLocators import CommonLocators, ContactFormLocators, DialogLocators


class HuurcontractBeeindigingFlow:
    """Hoofdklasse voor alle Huurcontract Beëindiging locators."""
    
    # Flow entry points
    ENTRY_ZELF_REGELEN_LINK = "//a[@href='/regelen']"
    ENTRY_HUURCONTRACT_LINK = "a[href='/regelen/huurcontract_beeindigen']"
    
    
class StapNavigatie(HuurcontractBeeindigingFlow):
    """Navigatie elementen specifiek voor deze flow."""
    
    # Flow-specifieke navigatie
    FLOW_PROGRESS_BAR = ".flow-progress"
    CURRENT_STEP_INDICATOR = ".current-step"
    

class Stap1_FlowStart(HuurcontractBeeindigingFlow):
    """Locators voor de eerste stap: Flow opstarten."""
    
    # Eerste dialoog vragen
    VRAAG_HUURCONTRACT_OPZEGGEN = "Huurcontract opzeggen"
    VRAAG_BLIJFT_IEMAND_WONEN = "Blijft er iemand in de woning wonen?"
    VRAAG_REDEN_OPZEGGING = "Reden huuropzegging"
    
    # Antwoord opties (deze worden gebruikt in de dialoog)
    ANTWOORD_DOORGAAN = "Doorgaan"
    ANTWOORD_NEE = "Nee"
    ANTWOORD_VERHUIZING = "Verhuizing"


class Stap2_DatumSelectie(HuurcontractBeeindigingFlow):
    """Locators voor datum selectie stap."""
    
    # Datum inputs
    INPUT_LAATSTE_HUURDAG = "[id='input-LaatsteHuurdag']"
    
    # Validatie berichten
    DATUM_ERROR = ".datum-error"
    DATUM_SUCCESS = ".datum-success"
    
    # Hulpteksten
    DATUM_HELP_TEXT = ".datum-help"


class Stap3_NieuwAdres(HuurcontractBeeindigingFlow):
    """Locators voor nieuw adres invullen."""
    
    # Dialoog vragen
    VRAAG_BUITENLAND = "Is je nieuwe postadres in het buitenland?"
    VRAAG_POSTADRES = "Wat is het (nieuwe) postadres?"
    
    # Adres formulier (hergebruikt van CommonLocators)
    # Deze zijn al gedefinieerd in ContactFormLocators
    
    # Adres validatie
    ADRES_VALIDATIE_SUCCESS = ".adres-valid"
    ADRES_VALIDATIE_ERROR = ".adres-error"
    
    # Auto-fill resultaten
    AUTO_FILL_STRAAT = "[id='input-Straatnaam']"
    AUTO_FILL_PLAATS = "[id='input-Woonplaats']"


class Stap4_PostDatum(HuurcontractBeeindigingFlow):
    """Locators voor post datum stap."""
    
    # Datum input
    INPUT_INGANGSDATUM_NIEUW_ADRES = "[id='input-IngangsdatumNieuwAdres']"
    
    # Vraag tekst
    VRAAG_POST_DATUM = "Per wanneer kunnen we post naar dit adres sturen?"


class Stap5_Veranderingen(HuurcontractBeeindigingFlow):
    """Locators voor zelf aangebrachte veranderingen."""
    
    # Vraag
    VRAAG_VERANDERINGEN = "Heb je iets veranderd aan je woning?"
    
    # Input voor veranderingen (hergebruikt van CommonLocators)
    # TEXTAREA_EXTRA_INFO is al gedefinieerd


class Stap6_Contactgegevens(HuurcontractBeeindigingFlow):
    """Locators voor contactgegevens stap."""
    
    # Contact inputs (hergebruikt van CommonLocators)
    # INPUT_TELEFOON1, INPUT_TELEFOON2, INPUT_EMAIL zijn al gedefinieerd
    
    # Validatie
    CONTACT_VALIDATIE_ERROR = ".contact-error"
    CONTACT_VALIDATIE_SUCCESS = ".contact-success"


class Stap7_Samenvatting(HuurcontractBeeindigingFlow):
    """Locators voor samenvattingsscherm."""
    
    # Samenvatting elementen
    SAMENVATTING_TITEL = "Samenvatting"
    SAMENVATTING_CONTENT = ".samenvatting-content"
    
    # Overzicht secties
    SECTIE_DATUM = ".samenvatting-datum"
    SECTIE_ADRES = ".samenvatting-adres"
    SECTIE_CONTACT = ".samenvatting-contact"
    SECTIE_VERANDERINGEN = ".samenvatting-veranderingen"
    
    # Bevestiging
    BUTTON_DEFINITIEF_INDIENEN = "//button[.='Definitief indienen']"
    CHECKBOX_AKKOORD = "[type='checkbox']"


# Voor backwards compatibility en eenvoudig gebruik
# Exporteer alle belangrijke locators als module-level variabelen

# Entry points
LINK_ZELF_REGELEN = HuurcontractBeeindigingFlow.ENTRY_ZELF_REGELEN_LINK
LINK_WONING = HuurcontractBeeindigingFlow.ENTRY_HUURCONTRACT_LINK

# Datum selectie
INPUT_LAATSTE_HUURDAG = Stap2_DatumSelectie.INPUT_LAATSTE_HUURDAG

# Nieuw adres (hergebruik van common locators)
INPUT_POSTCODE = ContactFormLocators.INPUT_POSTCODE
INPUT_HUISNUMMER = ContactFormLocators.INPUT_HUISNUMMER
INPUT_STRAATNAAM = ContactFormLocators.INPUT_STRAATNAAM
INPUT_WOONPLAATS = ContactFormLocators.INPUT_WOONPLAATS

# Post datum
INPUT_INGANGSDATUM_NIEUW_ADRES = Stap4_PostDatum.INPUT_INGANGSDATUM_NIEUW_ADRES

# Veranderingen
TEXTAREA_EXTRA_INFO = ContactFormLocators.TEXTAREA_EXTRA_INFO

# Contact
INPUT_TELEFOON1 = ContactFormLocators.INPUT_TELEFOON1
INPUT_TELEFOON2 = ContactFormLocators.INPUT_TELEFOON2
INPUT_EMAIL = ContactFormLocators.INPUT_EMAIL

# Samenvatting
SAMENVATTING_TITEL = Stap7_Samenvatting.SAMENVATTING_TITEL

# Common elements
PAGE_TITLE = CommonLocators.PAGE_TITLE
BUTTON_DOORGAAN = CommonLocators.BUTTON_DOORGAAN
