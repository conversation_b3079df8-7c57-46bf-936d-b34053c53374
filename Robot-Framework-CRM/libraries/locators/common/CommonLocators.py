"""
Gemeenschappelijke locators die in meerdere flows gebruikt worden.
Deze locators zijn herbruikbaar en niet specifiek voor één flow.
"""

class CommonLocators:
    """Basis locators die overal gebruikt kunnen worden."""
    
    # Buttons - Generieke knoppen die overal voorkomen
    BUTTON_DOORGAAN = "//button[.='Doorgaan']"
    BUTTON_TERUG = "//button[.='Terug']"
    BUTTON_ANNULEREN = "//button[.='Annuleren']"
    BUTTON_OPSLAAN = "//button[.='Opslaan']"
    BUTTON_BEVESTIGEN = "//button[.='Bevestigen']"
    BUTTON_AFSLUITEN = "//button[.='Afsluiten']"
    
    # Headers - Standaard header structuur
    PAGE_TITLE = "h2[class='font-display mb-4 text-3xl']"
    MAIN_HEADER = "h1"
    SUB_HEADER = "h3"
    
    # Loading en status indicatoren
    LOADING_INDICATOR = "div[role='status']"
    ERROR_MESSAGE = ".error-message"
    SUCCESS_MESSAGE = ".success-message"
    
    # Navigatie
    BREADCRUMB = ".breadcrumb"
    BACK_LINK = "//a[contains(text(), 'Terug')]"
    
    # Form elementen - Generieke form patterns
    REQUIRED_FIELD_INDICATOR = ".required"
    FORM_ERROR = ".form-error"
    FORM_SUCCESS = ".form-success"


class ContactFormLocators:
    """Locators voor contactgegevens formulieren - komen in meerdere flows voor."""
    
    # Contact inputs - Deze komen in bijna elke flow voor
    INPUT_TELEFOON1 = "[id='input-Telefoon1']"
    INPUT_TELEFOON2 = "[id='input-Telefoon2']"
    INPUT_EMAIL = "[id='input-Email']"
    
    # Adres inputs - Ook veel gebruikt
    INPUT_POSTCODE = "[id='input-Postcode']"
    INPUT_HUISNUMMER = "[id='input-Huisnummer']"
    INPUT_STRAATNAAM = "[id='input-Straatnaam']"
    INPUT_WOONPLAATS = "[id='input-Woonplaats']"
    
    # Extra informatie - Standaard textarea
    TEXTAREA_EXTRA_INFO = "[id='textarea-ExtraInfo']"


class DialogLocators:
    """Locators voor klantdialoog elementen."""
    
    # Dialog structuur
    DIALOG_QUESTION = "h2[class='font-display mb-4 text-3xl']"
    DIALOG_CONTENT = ".dialog-content"
    
    # Veel voorkomende antwoord knoppen
    BUTTON_JA = "//button[.='Ja']"
    BUTTON_NEE = "//button[.='Nee']"
    BUTTON_DOORGAAN = "//button[.='Doorgaan']"
    BUTTON_OVERSLAAN = "//button[.='Overslaan']"


# Voor backwards compatibility - exporteer alle locators als module-level variabelen
# Dit zorgt ervoor dat bestaande code blijft werken
BUTTON_DOORGAAN = CommonLocators.BUTTON_DOORGAAN
BUTTON_TERUG = CommonLocators.BUTTON_TERUG
PAGE_TITLE = CommonLocators.PAGE_TITLE
INPUT_TELEFOON1 = ContactFormLocators.INPUT_TELEFOON1
INPUT_TELEFOON2 = ContactFormLocators.INPUT_TELEFOON2
INPUT_EMAIL = ContactFormLocators.INPUT_EMAIL
INPUT_POSTCODE = ContactFormLocators.INPUT_POSTCODE
INPUT_HUISNUMMER = ContactFormLocators.INPUT_HUISNUMMER
INPUT_STRAATNAAM = ContactFormLocators.INPUT_STRAATNAAM
INPUT_WOONPLAATS = ContactFormLocators.INPUT_WOONPLAATS
TEXTAREA_EXTRA_INFO = ContactFormLocators.TEXTAREA_EXTRA_INFO
DIALOG_QUESTION = DialogLocators.DIALOG_QUESTION
