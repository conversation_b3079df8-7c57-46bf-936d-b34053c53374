"""
Utility functions for date handling in Robot Framework tests.
"""
from datetime import datetime, timedelta
from robot.api.deco import keyword



@keyword
def bereken_datum_in_de_toekomst_zonder_weekend_en_vrije_dagen(min_dagen_vooruit=31, excluded_dates=None):
    """
    Berekent een geschikte datum in de toekomst.

    Args:
        min_dagen_vooruit: Minimaal aantal dagen in de toekomst (standaard 35)
        excluded_dates: Lijst van uitgesloten datums (feestdagen) in formaat dd-mm-yyyy

    Returns:
        Een geldige datum in formaat dd-mm-yyyy die:
        - Een werkdag is (niet in het weekend)
        - Geen feestdag is
        - Minimaal het opgegeven aantal dagen in de toekomst ligt
    """
    if excluded_dates is None:
        excluded_dates = []

    # Huidige datum + minimaal aantal dagen vooruit
    start_date = datetime.now() + timedelta(days=min_dagen_vooruit)

    # Zoek een geldige datum
    for i in range(60):  # Zoek maximaal 60 dagen vooruit
        candidate_date = start_date + timedelta(days=i)

        # Check of het weekend is (5=zaterdag, 6=zondag)
        weekday = candidate_date.weekday()
        is_weekend = weekday >= 5

        # Converteer naar dd-mm-yyyy formaat voor vergelijking
        date_str = candidate_date.strftime("%d-%m-%Y")

        # Check of de datum in de uitgesloten lijst staat
        is_excluded = date_str in excluded_dates

        if not is_weekend and not is_excluded:
            return date_str

    # Als we hier komen, is er geen geldige datum gevonden
    raise Exception(f"Geen geldige datum gevonden binnen 60 dagen vanaf {min_dagen_vooruit} dagen vooruit")